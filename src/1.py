import pandas as pd
import numpy as np
from datetime import datetime

FH, KD, SK, YX, XN, HG, OEE, YJ = '设备负荷时间', '设备开动时间', '时间可用率', '有效生产时间', '性能开动率', '产品合格率', '设备OEE', '预警状态'

df = pd.read_excel('OEE设备数据表251.xlsx')

numeric_cols = ['工作总时间', '工间休息时间', '计划停机维护时间', '更换模具时间', '产品检验时间',
                '待机补料时间', '（工艺、质量）问题分析时间', '其他（断电）', '生产产量', '理想节拍',
                '设备预热启动时间', '工艺参数调试时间', '零件卡料空转时间', '操作失误调整时间', '废品量']
df[numeric_cols] = df[numeric_cols].fillna(0)

df[FH] = df['工作总时间'] - df['工间休息时间'] - df['计划停机维护时间']
df[KD] = df[FH] - df['更换模具时间'] - df['产品检验时间'] - df['待机补料时间'] - df['（工艺、质量）问题分析时间'] - df['其他（断电）']
df[SK] = np.where(df[FH] > 0, df[KD] / df[FH] * 100, 0)
df[YX] = df[KD] - df['设备预热启动时间'] - df['工艺参数调试时间'] - df['零件卡料空转时间'] - df['操作失误调整时间']
df[XN] = np.where(df[YX] > 0, (df['生产产量'] * df['理想节拍']) / df[YX] * 100, 0)
df[HG] = np.where(df['生产产量'] > 0, (df['生产产量'] - df['废品量']) / df['生产产量'] * 100, 0)
df[OEE] = df[SK] * df[XN] * df[HG] / 10000
df[YJ] = np.where(df[OEE] < 60, '效率过低', '正常')

output_filename = f'设备效率分析报告_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
df.to_excel(output_filename, index=False)
print(f"计算结果已保存到: {output_filename}")

alert_count = len(df[df[YJ] == '效率过低'])
total_days = len(df)

if alert_count > 0:
    min_row = df.loc[df[OEE].idxmin()]
    min_date, min_value = min_row['日期'].strftime('%Y-%m-%d'), min_row[OEE]

    causes = []
    if min_row[SK] < 85: causes.append(f"时间可用率低({min_row[SK]:.2f}%)")
    if min_row[XN] < 85: causes.append(f"性能开动率低({min_row[XN]:.2f}%)")
    if min_row[HG] < 95: causes.append(f"产品合格率低({min_row[HG]:.2f}%)")
    min_cause = " ".join(causes) if causes else "综合因素"
else:
    min_date, min_value, min_cause = "无", 100, "无"

print(f"""
设备效率分析报告摘要:
设备名称: 1号冲压线
分析日期: {df['日期'].min().strftime('%Y-%m-%d')} 至 {df['日期'].max().strftime('%Y-%m-%d')}
总天数: {total_days} 天
效率过低: {alert_count} 天 ({(alert_count/total_days)*100:.2f}%)
最低效率: {min_date} ({min_value:.2f}%)
主要原因: {min_cause}

平均指标:
设备OEE: {df[OEE].mean():.2f}%
时间可用率: {df[SK].mean():.2f}%
性能开动率: {df[XN].mean():.2f}%
产品合格率: {df[HG].mean():.2f}%
""")

import pandas as pd
import numpy as np
from datetime import datetime

FH, KD, SK, YX, XN, HG, OEE, YJ = '设备负荷时间', '设备开动时间', '时间可用率', '有效生产时间', '性能开动率', '产品合格率', '设备OEE', '预警状态'

df = pd.read_excel('OEE设备数据表251.xlsx')

numeric_cols = ['工作总时间', '工间休息时间', '计划停机维护时间', '更换模具时间', '产品检验时间',
                '待机补料时间', '（工艺、质量）问题分析时间', '其他（断电）', '生产产量', '理想节拍',
                '设备预热启动时间', '工艺参数调试时间', '零件卡料空转时间', '操作失误调整时间', '废品量']
df[numeric_cols] = df[numeric_cols].fillna(0)

df[FH] = df['工作总时间'] - df['工间休息时间'] - df['计划停机维护时间']
df[KD] = df[FH] - df['更换模具时间'] - df['产品检验时间'] - df['待机补料时间'] - df['（工艺、质量）问题分析时间'] - df['其他（断电）']
df[SK] = np.where(df[FH] > 0, df[KD] / df[FH] * 100, 0)
df[YX] = df[KD] - df['设备预热启动时间'] - df['工艺参数调试时间'] - df['零件卡料空转时间'] - df['操作失误调整时间']
df[XN] = np.where(df[YX] > 0, (df['生产产量'] * df['理想节拍']) / df[YX] * 100, 0)
df[HG] = np.where(df['生产产量'] > 0, (df['生产产量'] - df['废品量']) / df['生产产量'] * 100, 0)
df[OEE] = df[SK] * df[XN] * df[HG] / 10000
df[YJ] = np.where(df[OEE] < 60, '效率过低', '正常')

output_filename = f'设备效率分析报告_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
df.to_excel(output_filename, index=False)
print(f"计算结果已保存到: {output_filename}")

alert_count = len(df[df[YJ] == '效率过低'])
total_days = len(df)

if alert_count > 0:
    min_row = df.loc[df[OEE].idxmin()]
    min_date, min_value = min_row['日期'].strftime('%Y-%m-%d'), min_row[OEE]

    causes = []
    if min_row[SK] < 85: causes.append(f"时间可用率低({min_row[SK]:.2f}%)")
    if min_row[XN] < 85: causes.append(f"性能开动率低({min_row[XN]:.2f}%)")
    if min_row[HG] < 95: causes.append(f"产品合格率低({min_row[HG]:.2f}%)")
    min_cause = " ".join(causes) if causes else "综合因素"
else:
    min_date, min_value, min_cause = "无", 100, "无"

print(f"""
设备效率分析报告摘要:
设备名称: 1号冲压线
分析日期: {df['日期'].min().strftime('%Y-%m-%d')} 至 {df['日期'].max().strftime('%Y-%m-%d')}
总天数: {total_days} 天
效率过低: {alert_count} 天 ({(alert_count/total_days)*100:.2f}%)
最低效率: {min_date} ({min_value:.2f}%)
主要原因: {min_cause}

平均指标:
设备OEE: {df[OEE].mean():.2f}%
时间可用率: {df[SK].mean():.2f}%
性能开动率: {df[XN].mean():.2f}%
产品合格率: {df[HG].mean():.2f}%
""")