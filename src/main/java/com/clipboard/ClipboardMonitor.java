package com.clipboard;

import java.awt.*;
import java.awt.datatransfer.*;
import javax.swing.Timer;

/**
 * 剪切板监控器
 * 定时检查系统剪切板变化
 */
public class ClipboardMonitor implements ClipboardOwner {
    private final ClipboardHistory history;
    private final ClipboardGUI gui;
    private final Clipboard systemClipboard;
    private Timer monitorTimer;
    private String lastContent;
    private boolean monitoring;

    public ClipboardMonitor(ClipboardHistory history, ClipboardGUI gui) {
        this.history = history;
        this.gui = gui;
        this.systemClipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        this.monitoring = false;
        this.lastContent = "";
    }

    /**
     * 开始监控剪切板
     */
    public void startMonitoring() {
        if (monitoring) {
            return;
        }

        monitoring = true;
        
        // 获取当前剪切板内容作为初始状态
        String currentContent = getCurrentClipboardContent();
        if (currentContent != null && !currentContent.isEmpty()) {
            lastContent = currentContent;
            history.addItem(currentContent);
        }

        // 创建定时器，每500毫秒检查一次剪切板
        monitorTimer = new Timer(500, e -> checkClipboard());
        monitorTimer.start();

        System.out.println("剪切板监控已启动");
    }

    /**
     * 停止监控剪切板
     */
    public void stopMonitoring() {
        if (!monitoring) {
            return;
        }

        monitoring = false;
        
        if (monitorTimer != null) {
            monitorTimer.stop();
            monitorTimer = null;
        }

        System.out.println("剪切板监控已停止");
    }

    /**
     * 检查剪切板变化
     */
    private void checkClipboard() {
        try {
            String currentContent = getCurrentClipboardContent();
            
            if (currentContent != null && 
                !currentContent.equals(lastContent) && 
                !currentContent.trim().isEmpty()) {
                
                lastContent = currentContent;
                history.addItem(currentContent);
                
                // 更新GUI状态
                if (gui != null) {
                    gui.updateStatus("检测到新的剪切板内容");
                }
                
                System.out.println("检测到剪切板变化: " + 
                    (currentContent.length() > 30 ? 
                     currentContent.substring(0, 30) + "..." : currentContent));
            }
        } catch (Exception e) {
            // 静默处理异常，避免频繁输出错误信息
            if (e.getMessage() != null && !e.getMessage().contains("cannot open system clipboard")) {
                System.err.println("剪切板检查异常: " + e.getMessage());
            }
        }
    }

    /**
     * 获取当前剪切板内容
     */
    private String getCurrentClipboardContent() {
        try {
            if (!systemClipboard.isDataFlavorAvailable(DataFlavor.stringFlavor)) {
                return null;
            }
            
            Object data = systemClipboard.getData(DataFlavor.stringFlavor);
            return data != null ? data.toString() : null;
            
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 设置剪切板内容
     */
    public void setClipboardContent(String content) {
        try {
            StringSelection selection = new StringSelection(content);
            systemClipboard.setContents(selection, this);
            lastContent = content; // 更新最后内容，避免重复检测
            
            if (gui != null) {
                gui.updateStatus("已复制到剪切板");
            }
        } catch (Exception e) {
            System.err.println("设置剪切板内容失败: " + e.getMessage());
            if (gui != null) {
                gui.updateStatus("复制失败: " + e.getMessage());
            }
        }
    }

    /**
     * 检查是否正在监控
     */
    public boolean isMonitoring() {
        return monitoring;
    }

    /**
     * ClipboardOwner接口实现
     */
    @Override
    public void lostOwnership(Clipboard clipboard, Transferable contents) {
        // 当失去剪切板所有权时调用，这里不需要特殊处理
    }

    /**
     * 暂停监控（临时）
     */
    public void pauseMonitoring() {
        if (monitorTimer != null) {
            monitorTimer.stop();
        }
    }

    /**
     * 恢复监控
     */
    public void resumeMonitoring() {
        if (monitoring && monitorTimer != null) {
            monitorTimer.start();
        }
    }
}
