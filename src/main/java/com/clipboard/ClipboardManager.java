package com.clipboard;

import javax.swing.*;
import java.awt.*;

/**
 * 剪切板工具主程序
 * 功能：监控剪切板、历史记录、系统托盘、导出导入
 */
public class ClipboardManager {
    private static ClipboardGUI gui;
    private static ClipboardMonitor monitor;
    private static SystemTrayManager trayManager;
    private static ClipboardHistory history;
    private static DataManager dataManager;

    public static void main(String[] args) {
        // 设置系统外观
        try {
            // UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            // 忽略外观设置错误
        }

        // 检查系统托盘支持
        if (!SystemTray.isSupported()) {
            JOptionPane.showMessageDialog(null, 
                "系统不支持托盘功能", "错误", JOptionPane.ERROR_MESSAGE);
            System.exit(1);
        }

        // 初始化组件
        SwingUtilities.invokeLater(() -> {
            try {
                initializeComponents();
                startApplication();
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, 
                    "程序启动失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                System.exit(1);
            }
        });
    }

    private static void initializeComponents() {
        // 初始化历史记录管理器
        history = new ClipboardHistory();
        
        // 初始化数据管理器
        dataManager = new DataManager(history);
        
        // 初始化GUI
        gui = new ClipboardGUI(history, dataManager);
        
        // 初始化系统托盘
        trayManager = new SystemTrayManager(gui);
        
        // 初始化剪切板监控器
        monitor = new ClipboardMonitor(history, gui);
    }

    private static void startApplication() {
        // 启动剪切板监控
        monitor.startMonitoring();
        
        // 设置系统托盘
        trayManager.setupTray();
        
        // 显示主界面
        gui.setVisible(true);
        
        System.out.println("剪切板工具启动成功！");
    }

    // 获取各个组件的静态引用
    public static ClipboardGUI getGUI() {
        return gui;
    }

    public static ClipboardHistory getHistory() {
        return history;
    }

    public static DataManager getDataManager() {
        return dataManager;
    }

    public static void shutdown() {
        if (monitor != null) {
            monitor.stopMonitoring();
        }
        if (trayManager != null) {
            trayManager.removeTray();
        }
        System.exit(0);
    }
}
