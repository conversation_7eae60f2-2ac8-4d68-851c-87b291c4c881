package com.clipboard;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 剪切板历史记录管理
 * 按复制顺序存储，最先复制的在前面
 */
public class ClipboardHistory {
    private final List<ClipboardItem> history;
    private final Set<String> contentSet; // 用于快速查重
    private final int maxSize;
    private final List<HistoryChangeListener> listeners;

    public ClipboardHistory() {
        this(100); // 默认最多保存100条记录
    }

    public ClipboardHistory(int maxSize) {
        this.maxSize = maxSize;
        this.history = new CopyOnWriteArrayList<>();
        this.contentSet = new HashSet<>();
        this.listeners = new ArrayList<>();
    }

    /**
     * 添加新的剪切板内容
     */
    public synchronized void addItem(String content) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }

        content = content.trim();
        
        // 如果内容已存在，移除旧的
        if (contentSet.contains(content)) {
            removeItem(content);
        }

        // 创建新项目
        ClipboardItem item = new ClipboardItem(content, new Date());
        
        // 添加到列表开头（最新的在前面，但输出时会反转）
        history.add(0, item);
        contentSet.add(content);

        // 限制大小
        while (history.size() > maxSize) {
            ClipboardItem removed = history.remove(history.size() - 1);
            contentSet.remove(removed.getContent());
        }

        // 通知监听器
        notifyListeners();
    }

    /**
     * 移除指定内容
     */
    public synchronized void removeItem(String content) {
        history.removeIf(item -> item.getContent().equals(content));
        contentSet.remove(content);
        notifyListeners();
    }

    /**
     * 清空历史记录
     */
    public synchronized void clear() {
        history.clear();
        contentSet.clear();
        notifyListeners();
    }

    /**
     * 获取历史记录列表（按复制顺序，最先复制的在前面）
     */
    public List<ClipboardItem> getHistory() {
        List<ClipboardItem> result = new ArrayList<>(history);
        Collections.reverse(result); // 反转，使最先复制的在前面
        return result;
    }

    /**
     * 获取最新的历史记录列表（最新复制的在前面）
     */
    public List<ClipboardItem> getRecentHistory() {
        return new ArrayList<>(history);
    }

    /**
     * 获取历史记录数量
     */
    public int size() {
        return history.size();
    }

    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return history.isEmpty();
    }

    /**
     * 添加变化监听器
     */
    public void addListener(HistoryChangeListener listener) {
        listeners.add(listener);
    }

    /**
     * 移除变化监听器
     */
    public void removeListener(HistoryChangeListener listener) {
        listeners.remove(listener);
    }

    private void notifyListeners() {
        for (HistoryChangeListener listener : listeners) {
            try {
                listener.onHistoryChanged();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 剪切板项目类
     */
    public static class ClipboardItem {
        private final String content;
        private final Date timestamp;

        public ClipboardItem(String content, Date timestamp) {
            this.content = content;
            this.timestamp = timestamp;
        }

        public String getContent() {
            return content;
        }

        public Date getTimestamp() {
            return timestamp;
        }

        public String getPreview() {
            if (content.length() <= 50) {
                return content;
            }
            return content.substring(0, 47) + "...";
        }

        @Override
        public String toString() {
            return getPreview();
        }
    }

    /**
     * 历史记录变化监听器接口
     */
    public interface HistoryChangeListener {
        void onHistoryChanged();
    }
}
