package com.clipboard;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.image.BufferedImage;

/**
 * 系统托盘管理器
 * 处理系统托盘图标和菜单
 */
public class SystemTrayManager {
    private final ClipboardGUI gui;
    private SystemTray systemTray;
    private TrayIcon trayIcon;

    public SystemTrayManager(ClipboardGUI gui) {
        this.gui = gui;
    }

    /**
     * 设置系统托盘
     */
    public void setupTray() {
        if (!SystemTray.isSupported()) {
            System.err.println("系统不支持托盘功能");
            return;
        }

        try {
            systemTray = SystemTray.getSystemTray();
            
            // 创建托盘图标
            Image trayImage = createTrayIcon();
            
            // 创建弹出菜单
            PopupMenu popupMenu = createPopupMenu();
            
            // 创建托盘图标
            trayIcon = new TrayIcon(trayImage, "剪切板工具", popupMenu);
            trayIcon.setImageAutoSize(true);
            
            // 添加双击事件
            trayIcon.addActionListener(e -> showMainWindow());
            
            // 添加到系统托盘
            systemTray.add(trayIcon);
            
            System.out.println("系统托盘设置成功");
            
        } catch (AWTException e) {
            System.err.println("无法添加到系统托盘: " + e.getMessage());
        }
    }

    /**
     * 创建托盘图标
     */
    private Image createTrayIcon() {
        // 创建一个16x16的图标
        BufferedImage image = new BufferedImage(16, 16, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制背景圆形
        g2d.setColor(new Color(70, 130, 180)); // 钢蓝色
        g2d.fillOval(1, 1, 14, 14);
        
        // 绘制边框
        g2d.setColor(Color.WHITE);
        g2d.drawOval(1, 1, 14, 14);
        
        // 绘制剪切板图标（简化版）
        g2d.setColor(Color.WHITE);
        g2d.fillRect(4, 3, 8, 10);
        g2d.setColor(new Color(70, 130, 180));
        g2d.fillRect(5, 4, 6, 8);
        
        // 绘制夹子
        g2d.setColor(Color.WHITE);
        g2d.fillRect(6, 2, 4, 2);
        
        g2d.dispose();
        return image;
    }

    /**
     * 创建弹出菜单
     */
    private PopupMenu createPopupMenu() {
        PopupMenu popupMenu = new PopupMenu();
        
        // 显示主窗口
        MenuItem showItem = new MenuItem("显示主窗口");
        showItem.addActionListener(e -> showMainWindow());
        popupMenu.add(showItem);
        
        popupMenu.addSeparator();
        
        // 快速操作菜单
        MenuItem clearItem = new MenuItem("清空历史记录");
        clearItem.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(null,
                    "确定要清空所有历史记录吗？", "确认清空",
                    JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                ClipboardManager.getHistory().clear();
                showTrayMessage("历史记录已清空", TrayIcon.MessageType.INFO);
            }
        });
        popupMenu.add(clearItem);
        
        // 导出菜单
        MenuItem exportItem = new MenuItem("导出历史记录");
        exportItem.addActionListener(e -> {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
                    "JSON文件", "json"));
            
            if (fileChooser.showSaveDialog(null) == JFileChooser.APPROVE_OPTION) {
                try {
                    String filePath = fileChooser.getSelectedFile().getAbsolutePath();
                    if (!filePath.endsWith(".json")) {
                        filePath += ".json";
                    }
                    ClipboardManager.getDataManager().exportHistory(filePath);
                    showTrayMessage("导出成功", TrayIcon.MessageType.INFO);
                } catch (Exception ex) {
                    showTrayMessage("导出失败: " + ex.getMessage(), TrayIcon.MessageType.ERROR);
                }
            }
        });
        popupMenu.add(exportItem);
        
        popupMenu.addSeparator();
        
        // 关于
        MenuItem aboutItem = new MenuItem("关于");
        aboutItem.addActionListener(e -> showAbout());
        popupMenu.add(aboutItem);
        
        popupMenu.addSeparator();
        
        // 退出
        MenuItem exitItem = new MenuItem("退出");
        exitItem.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(null,
                    "确定要退出剪切板工具吗？", "确认退出",
                    JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                ClipboardManager.shutdown();
            }
        });
        popupMenu.add(exitItem);
        
        return popupMenu;
    }

    /**
     * 显示主窗口
     */
    private void showMainWindow() {
        SwingUtilities.invokeLater(() -> {
            gui.setVisible(true);
            gui.setExtendedState(JFrame.NORMAL);
            gui.toFront();
            gui.requestFocus();
        });
    }

    /**
     * 显示托盘消息
     */
    public void showTrayMessage(String message, TrayIcon.MessageType messageType) {
        if (trayIcon != null) {
            trayIcon.displayMessage("剪切板工具", message, messageType);
        }
    }

    /**
     * 显示关于对话框
     */
    private void showAbout() {
        String aboutText = "剪切板工具 v1.0\n\n" +
                "功能特性:\n" +
                "• 实时监控剪切板变化\n" +
                "• 按复制顺序显示历史记录\n" +
                "• 支持导出/导入历史记录\n" +
                "• 系统托盘后台运行\n" +
                "• 简洁易用的界面\n\n" +
                "使用说明:\n" +
                "• 双击托盘图标显示主窗口\n" +
                "• 双击列表项快速复制\n" +
                "• 支持JSON格式导出导入\n\n" +
                "开发: AI Assistant";
        
        JOptionPane.showMessageDialog(null, aboutText, "关于剪切板工具", 
                JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * 移除托盘图标
     */
    public void removeTray() {
        if (systemTray != null && trayIcon != null) {
            systemTray.remove(trayIcon);
            System.out.println("已从系统托盘移除");
        }
    }

    /**
     * 更新托盘图标提示
     */
    public void updateTrayTooltip(String tooltip) {
        if (trayIcon != null) {
            trayIcon.setToolTip(tooltip);
        }
    }

    /**
     * 检查托盘是否可用
     */
    public boolean isTrayAvailable() {
        return SystemTray.isSupported() && trayIcon != null;
    }
}
