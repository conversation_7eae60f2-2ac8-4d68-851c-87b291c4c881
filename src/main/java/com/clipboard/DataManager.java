package com.clipboard;

import javax.swing.*;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 数据管理器
 * 处理剪切板历史记录的导出和导入
 */
public class DataManager {
    private final ClipboardHistory history;
    private final SimpleDateFormat dateFormat;

    public DataManager(ClipboardHistory history) {
        this.history = history;
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 导出历史记录到JSON文件
     */
    public void exportHistory(String filePath) throws IOException {
        List<ClipboardHistory.ClipboardItem> items = history.getHistory();
        
        if (items.isEmpty()) {
            throw new IOException("没有历史记录可以导出");
        }

        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("{\n");
        jsonBuilder.append("  \"version\": \"1.0\",\n");
        jsonBuilder.append("  \"exportTime\": \"").append(dateFormat.format(new Date())).append("\",\n");
        jsonBuilder.append("  \"totalItems\": ").append(items.size()).append(",\n");
        jsonBuilder.append("  \"items\": [\n");

        for (int i = 0; i < items.size(); i++) {
            ClipboardHistory.ClipboardItem item = items.get(i);
            jsonBuilder.append("    {\n");
            jsonBuilder.append("      \"timestamp\": \"").append(dateFormat.format(item.getTimestamp())).append("\",\n");
            jsonBuilder.append("      \"content\": ").append(escapeJsonString(item.getContent())).append("\n");
            jsonBuilder.append("    }");
            
            if (i < items.size() - 1) {
                jsonBuilder.append(",");
            }
            jsonBuilder.append("\n");
        }

        jsonBuilder.append("  ]\n");
        jsonBuilder.append("}");

        // 写入文件
        try (FileWriter writer = new FileWriter(filePath, StandardCharsets.UTF_8)) {
            writer.write(jsonBuilder.toString());
        }

        System.out.println("成功导出 " + items.size() + " 条记录到: " + filePath);
    }

    /**
     * 从JSON文件导入历史记录
     */
    public void importHistory(String filePath) throws IOException {
        // 读取文件内容
        String jsonContent;
        try (FileReader reader = new FileReader(filePath, StandardCharsets.UTF_8)) {
            StringBuilder contentBuilder = new StringBuilder();
            char[] buffer = new char[1024];
            int bytesRead;
            while ((bytesRead = reader.read(buffer)) != -1) {
                contentBuilder.append(buffer, 0, bytesRead);
            }
            jsonContent = contentBuilder.toString();
        }

        // 简单的JSON解析（手动实现，避免依赖外部库）
        try {
            parseAndImportJson(jsonContent);
        } catch (Exception e) {
            throw new IOException("JSON文件格式错误: " + e.getMessage());
        }
    }

    /**
     * 解析JSON并导入数据
     */
    private void parseAndImportJson(String jsonContent) throws Exception {
        // 移除空白字符
        jsonContent = jsonContent.trim();
        
        if (!jsonContent.startsWith("{") || !jsonContent.endsWith("}")) {
            throw new Exception("无效的JSON格式");
        }

        // 查找items数组
        int itemsStart = jsonContent.indexOf("\"items\":");
        if (itemsStart == -1) {
            throw new Exception("找不到items字段");
        }

        // 找到数组开始位置
        int arrayStart = jsonContent.indexOf("[", itemsStart);
        int arrayEnd = jsonContent.lastIndexOf("]");
        
        if (arrayStart == -1 || arrayEnd == -1 || arrayStart >= arrayEnd) {
            throw new Exception("无效的items数组格式");
        }

        String itemsArray = jsonContent.substring(arrayStart + 1, arrayEnd).trim();
        
        if (itemsArray.isEmpty()) {
            System.out.println("导入的文件中没有历史记录");
            return;
        }

        // 解析每个项目
        int importCount = 0;
        int currentPos = 0;
        
        while (currentPos < itemsArray.length()) {
            // 跳过空白字符和逗号
            while (currentPos < itemsArray.length() && 
                   (Character.isWhitespace(itemsArray.charAt(currentPos)) || 
                    itemsArray.charAt(currentPos) == ',')) {
                currentPos++;
            }
            
            if (currentPos >= itemsArray.length()) {
                break;
            }

            // 找到对象开始
            if (itemsArray.charAt(currentPos) != '{') {
                throw new Exception("期望找到对象开始符号 '{'");
            }

            // 找到对象结束
            int objectEnd = findObjectEnd(itemsArray, currentPos);
            if (objectEnd == -1) {
                throw new Exception("找不到对象结束符号 '}'");
            }

            String objectStr = itemsArray.substring(currentPos, objectEnd + 1);
            
            // 解析单个对象
            String content = extractJsonValue(objectStr, "content");
            String timestampStr = extractJsonValue(objectStr, "timestamp");
            
            if (content != null && !content.trim().isEmpty()) {
                // 解析时间戳（如果解析失败，使用当前时间）
                Date timestamp;
                try {
                    timestamp = dateFormat.parse(timestampStr);
                } catch (Exception e) {
                    timestamp = new Date();
                }
                
                // 添加到历史记录
                history.addItem(content);
                importCount++;
            }

            currentPos = objectEnd + 1;
        }

        System.out.println("成功导入 " + importCount + " 条记录");

        // 显示导入结果
        final int finalImportCount = importCount;
        SwingUtilities.invokeLater(() -> {
            JOptionPane.showMessageDialog(null,
                "成功导入 " + finalImportCount + " 条历史记录",
                "导入完成", JOptionPane.INFORMATION_MESSAGE);
        });
    }

    /**
     * 找到JSON对象的结束位置
     */
    private int findObjectEnd(String str, int start) {
        int braceCount = 0;
        boolean inString = false;
        boolean escaped = false;

        for (int i = start; i < str.length(); i++) {
            char c = str.charAt(i);
            
            if (escaped) {
                escaped = false;
                continue;
            }
            
            if (c == '\\') {
                escaped = true;
                continue;
            }
            
            if (c == '"') {
                inString = !inString;
                continue;
            }
            
            if (!inString) {
                if (c == '{') {
                    braceCount++;
                } else if (c == '}') {
                    braceCount--;
                    if (braceCount == 0) {
                        return i;
                    }
                }
            }
        }
        
        return -1;
    }

    /**
     * 从JSON对象字符串中提取指定字段的值
     */
    private String extractJsonValue(String objectStr, String fieldName) {
        String searchPattern = "\"" + fieldName + "\":";
        int fieldStart = objectStr.indexOf(searchPattern);
        
        if (fieldStart == -1) {
            return null;
        }
        
        int valueStart = fieldStart + searchPattern.length();
        
        // 跳过空白字符
        while (valueStart < objectStr.length() && 
               Character.isWhitespace(objectStr.charAt(valueStart))) {
            valueStart++;
        }
        
        if (valueStart >= objectStr.length()) {
            return null;
        }
        
        // 如果值是字符串（以引号开始）
        if (objectStr.charAt(valueStart) == '"') {
            valueStart++; // 跳过开始引号
            StringBuilder value = new StringBuilder();
            boolean escaped = false;
            
            for (int i = valueStart; i < objectStr.length(); i++) {
                char c = objectStr.charAt(i);
                
                if (escaped) {
                    // 处理转义字符
                    switch (c) {
                        case 'n': value.append('\n'); break;
                        case 't': value.append('\t'); break;
                        case 'r': value.append('\r'); break;
                        case '\\': value.append('\\'); break;
                        case '"': value.append('"'); break;
                        default: value.append(c); break;
                    }
                    escaped = false;
                } else if (c == '\\') {
                    escaped = true;
                } else if (c == '"') {
                    return value.toString();
                } else {
                    value.append(c);
                }
            }
        }
        
        return null;
    }

    /**
     * 转义JSON字符串
     */
    private String escapeJsonString(String str) {
        if (str == null) {
            return "null";
        }
        
        StringBuilder escaped = new StringBuilder("\"");
        
        for (char c : str.toCharArray()) {
            switch (c) {
                case '"':
                    escaped.append("\\\"");
                    break;
                case '\\':
                    escaped.append("\\\\");
                    break;
                case '\n':
                    escaped.append("\\n");
                    break;
                case '\r':
                    escaped.append("\\r");
                    break;
                case '\t':
                    escaped.append("\\t");
                    break;
                default:
                    if (c < 32) {
                        escaped.append(String.format("\\u%04x", (int) c));
                    } else {
                        escaped.append(c);
                    }
                    break;
            }
        }
        
        escaped.append("\"");
        return escaped.toString();
    }

    /**
     * 导出为纯文本格式
     */
    public void exportAsText(String filePath) throws IOException {
        List<ClipboardHistory.ClipboardItem> items = history.getHistory();
        
        if (items.isEmpty()) {
            throw new IOException("没有历史记录可以导出");
        }

        try (FileWriter writer = new FileWriter(filePath, StandardCharsets.UTF_8)) {
            writer.write("剪切板历史记录导出\n");
            writer.write("导出时间: " + dateFormat.format(new Date()) + "\n");
            writer.write("总计: " + items.size() + " 条记录\n");
            writer.write("=" + "=".repeat(50) + "\n\n");

            for (int i = 0; i < items.size(); i++) {
                ClipboardHistory.ClipboardItem item = items.get(i);
                writer.write(String.format("[%d] %s\n", i + 1, dateFormat.format(item.getTimestamp())));
                writer.write(item.getContent());
                writer.write("\n" + "-".repeat(50) + "\n\n");
            }
        }

        System.out.println("成功导出 " + items.size() + " 条记录到文本文件: " + filePath);
    }
}
