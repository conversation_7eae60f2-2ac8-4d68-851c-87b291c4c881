package com.clipboard;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.*;
import java.awt.image.BufferedImage;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 剪切板工具主界面
 */
public class ClipboardGUI extends J<PERSON>rame implements ClipboardHistory.HistoryChangeListener {
    private final ClipboardHistory history;
    private final DataManager dataManager;
    private JList<ClipboardHistory.ClipboardItem> historyList;
    private DefaultListModel<ClipboardHistory.ClipboardItem> listModel;
    private JLabel statusLabel;
    private JButton copyButton;
    private JButton deleteButton;
    private JButton clearButton;
    private JTextArea previewArea;
    private final SimpleDateFormat dateFormat;
    private JCheckBox charByCharMode;
    private JSpinner charDelaySpinner;
    private boolean isCharByCharEnabled = false;
    private JWindow previewWindow;
    private JLabel previewLabel;
    private String currentText = "";
    private int currentCharIndex = 0;
    private boolean isCharByCharActive = false;

    public ClipboardGUI(ClipboardHistory history, DataManager dataManager) {
        this.history = history;
        this.dataManager = dataManager;
        this.dateFormat = new SimpleDateFormat("MM-dd HH:mm:ss");
        
        initializeGUI();
        setupEventHandlers();
        setupGlobalHotkeys();
        createPreviewWindow();

        // 注册为历史记录监听器
        history.addListener(this);
    }

    private void initializeGUI() {
        setTitle("剪切板工具 v1.0");
        setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);
        setSize(600, 500);
        setLocationRelativeTo(null);
        
        // 设置图标
        try {
            setIconImage(createDefaultIcon());
        } catch (Exception e) {
            System.err.println("设置图标失败: " + e.getMessage());
        }

        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));

        // 创建工具栏
        JPanel toolBar = createToolBar();
        mainPanel.add(toolBar, BorderLayout.NORTH);

        // 创建中央面板
        JPanel centerPanel = createCenterPanel();
        mainPanel.add(centerPanel, BorderLayout.CENTER);

        // 创建状态栏
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(new EmptyBorder(5, 0, 0, 0));
        mainPanel.add(statusLabel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    private JPanel createToolBar() {
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));

        copyButton = new JButton("复制选中");
        deleteButton = new JButton("删除选中");
        clearButton = new JButton("清空历史");
        JButton exportButton = new JButton("导出");
        JButton importButton = new JButton("导入");
        JButton hideButton = new JButton("隐藏到托盘");

        // 初始状态禁用某些按钮
        copyButton.setEnabled(false);
        deleteButton.setEnabled(false);

        toolBar.add(copyButton);
        toolBar.add(deleteButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(clearButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(exportButton);
        toolBar.add(importButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));

        // 添加逐字符模式控件
        charByCharMode = new JCheckBox("逐字符模式");
        charByCharMode.setToolTipText("启用后，粘贴多行文本时将逐字符输出");
        toolBar.add(charByCharMode);

        toolBar.add(new JLabel("延迟(ms):"));
        charDelaySpinner = new JSpinner(new SpinnerNumberModel(50, 10, 1000, 10));
        charDelaySpinner.setPreferredSize(new Dimension(80, 25));
        charDelaySpinner.setToolTipText("每个字符之间的延迟时间（毫秒）");
        toolBar.add(charDelaySpinner);

        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(hideButton);

        return toolBar;
    }

    private JPanel createCenterPanel() {
        JPanel centerPanel = new JPanel(new BorderLayout(5, 5));

        // 创建历史记录列表
        listModel = new DefaultListModel<>();
        historyList = new JList<>(listModel);
        historyList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        historyList.setCellRenderer(new ClipboardItemRenderer());
        
        JScrollPane listScrollPane = new JScrollPane(historyList);
        listScrollPane.setPreferredSize(new Dimension(300, 0));

        // 创建预览区域
        previewArea = new JTextArea();
        previewArea.setEditable(false);
        previewArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        previewArea.setBackground(getBackground());
        previewArea.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JScrollPane previewScrollPane = new JScrollPane(previewArea);
        previewScrollPane.setBorder(BorderFactory.createTitledBorder("内容预览"));

        // 使用分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, 
                                            listScrollPane, previewScrollPane);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.5);

        centerPanel.add(splitPane, BorderLayout.CENTER);

        return centerPanel;
    }

    private void setupEventHandlers() {
        // 窗口关闭事件
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                setVisible(false); // 隐藏而不是退出
            }
        });

        // 列表选择事件
        historyList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                updateButtonStates();
                updatePreview();
            }
        });

        // 双击复制
        historyList.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 2) {
                    copySelectedItem();
                }
            }
        });

        // 按钮事件
        copyButton.addActionListener(e -> copySelectedItem());
        deleteButton.addActionListener(e -> deleteSelectedItem());
        clearButton.addActionListener(e -> clearHistory());

        // 逐字符模式事件
        charByCharMode.addActionListener(e -> {
            isCharByCharEnabled = charByCharMode.isSelected();
            charDelaySpinner.setEnabled(isCharByCharEnabled);
            if (!isCharByCharEnabled && isCharByCharActive) {
                stopCharByCharMode();
            }
            updateStatus(isCharByCharEnabled ? "逐字符模式已启用 (Alt+V开始)" : "逐字符模式已禁用");
        });
        
        // 导出导入按钮
        getRootPane().getComponent(0); // 获取主面板
        Component[] components = ((JPanel)((JPanel)getRootPane().getContentPane()).getComponent(0)).getComponents();
        JPanel toolBar = (JPanel) components[0];
        Component[] toolBarComponents = toolBar.getComponents();
        
        // 找到导出导入按钮并添加事件
        for (Component comp : toolBarComponents) {
            if (comp instanceof JButton) {
                JButton btn = (JButton) comp;
                if ("导出".equals(btn.getText())) {
                    btn.addActionListener(e -> exportHistory());
                } else if ("导入".equals(btn.getText())) {
                    btn.addActionListener(e -> importHistory());
                } else if ("隐藏到托盘".equals(btn.getText())) {
                    btn.addActionListener(e -> setVisible(false));
                }
            }
        }
    }

    private void updateButtonStates() {
        boolean hasSelection = historyList.getSelectedIndex() != -1;
        copyButton.setEnabled(hasSelection);
        deleteButton.setEnabled(hasSelection);
        clearButton.setEnabled(!history.isEmpty());
    }

    private void updatePreview() {
        ClipboardHistory.ClipboardItem selected = historyList.getSelectedValue();
        if (selected != null) {
            String preview = String.format("时间: %s\n长度: %d 字符\n\n内容:\n%s",
                    dateFormat.format(selected.getTimestamp()),
                    selected.getContent().length(),
                    selected.getContent());
            previewArea.setText(preview);
            previewArea.setCaretPosition(0);
        } else {
            previewArea.setText("");
        }
    }

    private void copySelectedItem() {
        ClipboardHistory.ClipboardItem selected = historyList.getSelectedValue();
        if (selected != null) {
            String content = selected.getContent();

            if (isCharByCharEnabled && content.contains("\n")) {
                // 逐字符模式处理多行文本
                prepareCharByCharMode(content);
            } else {
                // 普通复制模式
                try {
                    java.awt.datatransfer.StringSelection selection =
                        new java.awt.datatransfer.StringSelection(content);
                    java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                        .setContents(selection, null);
                    updateStatus("已复制到剪切板");
                } catch (Exception e) {
                    updateStatus("复制失败: " + e.getMessage());
                }
            }
        }
    }

    private void deleteSelectedItem() {
        ClipboardHistory.ClipboardItem selected = historyList.getSelectedValue();
        if (selected != null) {
            int result = JOptionPane.showConfirmDialog(this,
                    "确定要删除这条记录吗？", "确认删除",
                    JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                history.removeItem(selected.getContent());
            }
        }
    }

    private void clearHistory() {
        int result = JOptionPane.showConfirmDialog(this,
                "确定要清空所有历史记录吗？", "确认清空",
                JOptionPane.YES_NO_OPTION);
        if (result == JOptionPane.YES_OPTION) {
            history.clear();
        }
    }

    private void exportHistory() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
                "JSON文件", "json"));
        
        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                String filePath = fileChooser.getSelectedFile().getAbsolutePath();
                if (!filePath.endsWith(".json")) {
                    filePath += ".json";
                }
                dataManager.exportHistory(filePath);
                updateStatus("导出成功: " + filePath);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this, "导出失败: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void importHistory() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
                "JSON文件", "json"));
        
        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                String filePath = fileChooser.getSelectedFile().getAbsolutePath();
                dataManager.importHistory(filePath);
                updateStatus("导入成功: " + filePath);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this, "导入失败: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    public void updateStatus(String message) {
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText(message);
            // 3秒后恢复默认状态
            Timer timer = new Timer(3000, e -> statusLabel.setText("就绪"));
            timer.setRepeats(false);
            timer.start();
        });
    }

    @Override
    public void onHistoryChanged() {
        SwingUtilities.invokeLater(() -> {
            listModel.clear();
            List<ClipboardHistory.ClipboardItem> items = history.getHistory();
            for (ClipboardHistory.ClipboardItem item : items) {
                listModel.addElement(item);
            }
            updateButtonStates();
        });
    }

    private void createPreviewWindow() {
        previewWindow = new JWindow();
        previewWindow.setAlwaysOnTop(true);

        previewLabel = new JLabel("", SwingConstants.CENTER);
        previewLabel.setFont(new Font(Font.MONOSPACED, Font.BOLD, 24));
        previewLabel.setForeground(Color.WHITE);
        previewLabel.setOpaque(true);
        previewLabel.setBackground(new Color(0, 0, 0, 180));
        previewLabel.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));

        previewWindow.add(previewLabel);
        previewWindow.setSize(100, 60);

        // 定位到右下角
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        previewWindow.setLocation(screenSize.width - 120, screenSize.height - 100);
    }

    private void setupGlobalHotkeys() {
        // 注册Alt+V热键
        KeyboardFocusManager.getCurrentKeyboardFocusManager().addKeyEventDispatcher(new KeyEventDispatcher() {
            @Override
            public boolean dispatchKeyEvent(KeyEvent e) {
                if (e.getID() == KeyEvent.KEY_PRESSED &&
                    e.getKeyCode() == KeyEvent.VK_V &&
                    e.isAltDown() &&
                    isCharByCharEnabled &&
                    isCharByCharActive) {

                    outputNextCharacter();
                    return true; // 消费事件
                }
                return false;
            }
        });
    }

    private void prepareCharByCharMode(String content) {
        currentText = content;
        currentCharIndex = 0;
        isCharByCharActive = true;

        updateStatus("逐字符模式已准备就绪，按 Alt+V 输出下一个字符");
        updatePreviewWindow();
        previewWindow.setVisible(true);
    }

    private void outputNextCharacter() {
        if (!isCharByCharActive || currentCharIndex >= currentText.length()) {
            stopCharByCharMode();
            return;
        }

        char currentChar = currentText.charAt(currentCharIndex);

        try {
            // 设置当前字符到剪切板
            String charStr = String.valueOf(currentChar);
            java.awt.datatransfer.StringSelection selection =
                new java.awt.datatransfer.StringSelection(charStr);
            java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                .setContents(selection, null);

            // 更新状态
            updateStatus(String.format("已输出字符: '%s' (%d/%d)",
                currentChar == '\n' ? "\\n" :
                currentChar == '\t' ? "\\t" :
                currentChar == '\r' ? "\\r" : String.valueOf(currentChar),
                currentCharIndex + 1, currentText.length()));

            currentCharIndex++;
            updatePreviewWindow();

            // 如果是最后一个字符，完成输出
            if (currentCharIndex >= currentText.length()) {
                Timer timer = new Timer(1000, e -> stopCharByCharMode());
                timer.setRepeats(false);
                timer.start();
            }

        } catch (Exception e) {
            updateStatus("输出字符失败: " + e.getMessage());
            stopCharByCharMode();
        }
    }

    private void updatePreviewWindow() {
        if (currentCharIndex < currentText.length()) {
            char nextChar = currentText.charAt(currentCharIndex);
            String displayChar = nextChar == '\n' ? "↵" :
                                nextChar == '\t' ? "→" :
                                nextChar == '\r' ? "⏎" :
                                nextChar == ' ' ? "␣" : String.valueOf(nextChar);
            previewLabel.setText(displayChar);
            previewWindow.pack();
        }
    }

    private void stopCharByCharMode() {
        isCharByCharActive = false;
        previewWindow.setVisible(false);
        updateStatus("逐字符模式已结束");
    }

    private Image createDefaultIcon() {
        // 创建一个简单的默认图标
        BufferedImage icon = new BufferedImage(16, 16, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = icon.createGraphics();
        g2d.setColor(Color.BLUE);
        g2d.fillRect(2, 2, 12, 12);
        g2d.setColor(Color.WHITE);
        g2d.fillRect(4, 4, 8, 8);
        g2d.dispose();
        return icon;
    }

    /**
     * 自定义列表渲染器
     */
    private class ClipboardItemRenderer extends DefaultListCellRenderer {
        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value,
                int index, boolean isSelected, boolean cellHasFocus) {
            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
            
            if (value instanceof ClipboardHistory.ClipboardItem) {
                ClipboardHistory.ClipboardItem item = (ClipboardHistory.ClipboardItem) value;
                String text = String.format("[%s] %s", 
                        dateFormat.format(item.getTimestamp()),
                        item.getPreview());
                setText(text);
            }
            
            return this;
        }
    }
}
