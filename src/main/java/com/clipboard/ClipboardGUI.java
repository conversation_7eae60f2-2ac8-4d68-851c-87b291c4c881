package com.clipboard;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.*;
import java.awt.image.BufferedImage;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 剪切板工具主界面
 */
public class ClipboardGUI extends J<PERSON>rame implements ClipboardHistory.HistoryChangeListener {
    private final ClipboardHistory history;
    private final DataManager dataManager;
    private JList<ClipboardHistory.ClipboardItem> historyList;
    private DefaultListModel<ClipboardHistory.ClipboardItem> listModel;
    private JLabel statusLabel;
    private JButton copyButton;
    private JButton deleteButton;
    private JButton clearButton;
    private JTextArea previewArea;
    private final SimpleDateFormat dateFormat;
    private JCheckBox charByCharMode;
    private JSpinner charDelaySpinner;
    private boolean isCharByCharEnabled = false;
    private JWindow previewWindow;
    private JLabel previewLabel;
    private String currentText = "";
    private int currentCharIndex = 0;
    private boolean isCharByCharActive = false;
    private boolean isSmartInputMode = false;
    private String[] currentWords;
    private int currentWordIndex = 0;
    private StringBuilder currentTypedWord = new StringBuilder();
    private Timer inputTimer;

    public ClipboardGUI(ClipboardHistory history, DataManager dataManager) {
        this.history = history;
        this.dataManager = dataManager;
        this.dateFormat = new SimpleDateFormat("MM-dd HH:mm:ss");
        
        initializeGUI();
        setupEventHandlers();
        setupGlobalHotkeys();
        createPreviewWindow();

        // 注册为历史记录监听器
        history.addListener(this);
    }

    private void initializeGUI() {
        setTitle("剪切板工具 v1.0");
        setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);
        setSize(600, 500);
        setLocationRelativeTo(null);
        
        // 设置图标
        try {
            setIconImage(createDefaultIcon());
        } catch (Exception e) {
            System.err.println("设置图标失败: " + e.getMessage());
        }

        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));

        // 创建工具栏
        JPanel toolBar = createToolBar();
        mainPanel.add(toolBar, BorderLayout.NORTH);

        // 创建中央面板
        JPanel centerPanel = createCenterPanel();
        mainPanel.add(centerPanel, BorderLayout.CENTER);

        // 创建状态栏
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(new EmptyBorder(5, 0, 0, 0));
        mainPanel.add(statusLabel, BorderLayout.SOUTH);

        add(mainPanel);
    }

    private JPanel createToolBar() {
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));

        copyButton = new JButton("复制选中");
        deleteButton = new JButton("删除选中");
        clearButton = new JButton("清空历史");
        JButton exportButton = new JButton("导出");
        JButton importButton = new JButton("导入");
        JButton addCodeButton = new JButton("录入代码");
        addCodeButton.setToolTipText("手动录入要练习的代码");
        addCodeButton.addActionListener(e -> showCodeInputDialog());
        JButton hideButton = new JButton("隐藏到托盘");

        // 初始状态禁用某些按钮
        copyButton.setEnabled(false);
        deleteButton.setEnabled(false);

        toolBar.add(copyButton);
        toolBar.add(deleteButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(clearButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(exportButton);
        toolBar.add(importButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(addCodeButton);
        toolBar.add(new JSeparator(SwingConstants.VERTICAL));

        // 添加智能输入模式控件
        charByCharMode = new JCheckBox("逐字符模式");
        charByCharMode.setToolTipText("启用后，点击'下一个字符'按钮逐字符输出");
        toolBar.add(charByCharMode);

        JCheckBox smartInputMode = new JCheckBox("智能输入助手");
        smartInputMode.setToolTipText("启用后，右下角显示下一个要输入的词，监控输入正确性");
        smartInputMode.addActionListener(e -> {
            isSmartInputMode = smartInputMode.isSelected();
            if (isSmartInputMode) {
                startSmartInputMode();
            } else {
                stopSmartInputMode();
            }
        });
        toolBar.add(smartInputMode);

        JButton nextCharButton = new JButton("下一个字符");
        nextCharButton.setToolTipText("输出下一个字符到剪切板");
        nextCharButton.addActionListener(e -> outputNextCharacter());
        toolBar.add(nextCharButton);

        toolBar.add(new JSeparator(SwingConstants.VERTICAL));
        toolBar.add(hideButton);

        return toolBar;
    }

    private JPanel createCenterPanel() {
        JPanel centerPanel = new JPanel(new BorderLayout(5, 5));

        // 创建历史记录列表
        listModel = new DefaultListModel<>();
        historyList = new JList<>(listModel);
        historyList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        historyList.setCellRenderer(new ClipboardItemRenderer());
        
        JScrollPane listScrollPane = new JScrollPane(historyList);
        listScrollPane.setPreferredSize(new Dimension(300, 0));

        // 创建预览区域
        previewArea = new JTextArea();
        previewArea.setEditable(false);
        previewArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        previewArea.setBackground(getBackground());
        previewArea.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JScrollPane previewScrollPane = new JScrollPane(previewArea);
        previewScrollPane.setBorder(BorderFactory.createTitledBorder("内容预览"));

        // 使用分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, 
                                            listScrollPane, previewScrollPane);
        splitPane.setDividerLocation(300);
        splitPane.setResizeWeight(0.5);

        centerPanel.add(splitPane, BorderLayout.CENTER);

        return centerPanel;
    }

    private void setupEventHandlers() {
        // 窗口关闭事件
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                setVisible(false); // 隐藏而不是退出
            }
        });

        // 列表选择事件
        historyList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                updateButtonStates();
                updatePreview();
            }
        });

        // 双击复制
        historyList.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                if (e.getClickCount() == 2) {
                    copySelectedItem();
                }
            }
        });

        // 按钮事件
        copyButton.addActionListener(e -> copySelectedItem());
        deleteButton.addActionListener(e -> deleteSelectedItem());
        clearButton.addActionListener(e -> clearHistory());

        // 逐字符模式事件
        charByCharMode.addActionListener(e -> {
            isCharByCharEnabled = charByCharMode.isSelected();
            if (!isCharByCharEnabled && isCharByCharActive) {
                stopCharByCharMode();
            }
            updateStatus(isCharByCharEnabled ? "逐字符模式已启用" : "逐字符模式已禁用");
        });
        
        // 导出导入按钮
        getRootPane().getComponent(0); // 获取主面板
        Component[] components = ((JPanel)((JPanel)getRootPane().getContentPane()).getComponent(0)).getComponents();
        JPanel toolBar = (JPanel) components[0];
        Component[] toolBarComponents = toolBar.getComponents();
        
        // 找到导出导入按钮并添加事件
        for (Component comp : toolBarComponents) {
            if (comp instanceof JButton) {
                JButton btn = (JButton) comp;
                if ("导出".equals(btn.getText())) {
                    btn.addActionListener(e -> exportHistory());
                } else if ("导入".equals(btn.getText())) {
                    btn.addActionListener(e -> importHistory());
                } else if ("隐藏到托盘".equals(btn.getText())) {
                    btn.addActionListener(e -> setVisible(false));
                }
            }
        }
    }

    private void updateButtonStates() {
        boolean hasSelection = historyList.getSelectedIndex() != -1;
        copyButton.setEnabled(hasSelection);
        deleteButton.setEnabled(hasSelection);
        clearButton.setEnabled(!history.isEmpty());
    }

    private void updatePreview() {
        ClipboardHistory.ClipboardItem selected = historyList.getSelectedValue();
        if (selected != null) {
            String preview = String.format("时间: %s\n长度: %d 字符\n\n内容:\n%s",
                    dateFormat.format(selected.getTimestamp()),
                    selected.getContent().length(),
                    selected.getContent());
            previewArea.setText(preview);
            previewArea.setCaretPosition(0);
        } else {
            previewArea.setText("");
        }
    }

    private void copySelectedItem() {
        ClipboardHistory.ClipboardItem selected = historyList.getSelectedValue();
        if (selected != null) {
            String content = selected.getContent();

            if (isCharByCharEnabled && content.contains("\n")) {
                // 逐字符模式处理多行文本
                prepareCharByCharMode(content);
            } else {
                // 普通复制模式
                try {
                    java.awt.datatransfer.StringSelection selection =
                        new java.awt.datatransfer.StringSelection(content);
                    java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                        .setContents(selection, null);
                    updateStatus("已复制到剪切板");
                } catch (Exception e) {
                    updateStatus("复制失败: " + e.getMessage());
                }
            }
        }
    }

    private void deleteSelectedItem() {
        ClipboardHistory.ClipboardItem selected = historyList.getSelectedValue();
        if (selected != null) {
            int result = JOptionPane.showConfirmDialog(this,
                    "确定要删除这条记录吗？", "确认删除",
                    JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                history.removeItem(selected.getContent());
            }
        }
    }

    private void clearHistory() {
        int result = JOptionPane.showConfirmDialog(this,
                "确定要清空所有历史记录吗？", "确认清空",
                JOptionPane.YES_NO_OPTION);
        if (result == JOptionPane.YES_OPTION) {
            history.clear();
        }
    }

    private void exportHistory() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
                "JSON文件", "json"));
        
        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                String filePath = fileChooser.getSelectedFile().getAbsolutePath();
                if (!filePath.endsWith(".json")) {
                    filePath += ".json";
                }
                dataManager.exportHistory(filePath);
                updateStatus("导出成功: " + filePath);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this, "导出失败: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    private void importHistory() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
                "JSON文件", "json"));
        
        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            try {
                String filePath = fileChooser.getSelectedFile().getAbsolutePath();
                dataManager.importHistory(filePath);
                updateStatus("导入成功: " + filePath);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(this, "导入失败: " + e.getMessage(),
                        "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    public void updateStatus(String message) {
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText(message);
            // 3秒后恢复默认状态
            Timer timer = new Timer(3000, e -> statusLabel.setText("就绪"));
            timer.setRepeats(false);
            timer.start();
        });
    }

    @Override
    public void onHistoryChanged() {
        SwingUtilities.invokeLater(() -> {
            listModel.clear();
            List<ClipboardHistory.ClipboardItem> items = history.getHistory();
            for (ClipboardHistory.ClipboardItem item : items) {
                listModel.addElement(item);
            }
            updateButtonStates();
        });
    }

    private void createPreviewWindow() {
        previewWindow = new JWindow();
        previewWindow.setAlwaysOnTop(true);

        previewLabel = new JLabel("", SwingConstants.CENTER);
        previewLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 16));
        previewLabel.setForeground(Color.WHITE);
        previewLabel.setOpaque(true);
        previewLabel.setBackground(new Color(0, 0, 0, 200));
        previewLabel.setBorder(BorderFactory.createEmptyBorder(8, 12, 8, 12));

        previewWindow.add(previewLabel);

        // 定位到右下角
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        previewWindow.setLocation(screenSize.width - 200, screenSize.height - 80);
    }

    private void setupGlobalHotkeys() {
        // 为主窗口添加键盘监听
        this.addKeyListener(new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_SPACE &&
                    isCharByCharEnabled &&
                    isCharByCharActive) {
                    outputNextCharacter();
                }
            }
        });
        this.setFocusable(true);
    }

    private void prepareCharByCharMode(String content) {
        currentText = content;
        currentCharIndex = 0;
        isCharByCharActive = true;

        updateStatus("逐字符模式已准备就绪，点击'下一个字符'按钮或按空格键输出");
        updatePreviewWindow();
        previewWindow.setVisible(true);

        // 确保窗口获得焦点以接收键盘事件
        this.requestFocus();
    }

    private void outputNextCharacter() {
        if (!isCharByCharActive || currentCharIndex >= currentText.length()) {
            stopCharByCharMode();
            return;
        }

        char currentChar = currentText.charAt(currentCharIndex);

        try {
            // 设置当前字符到剪切板
            String charStr = String.valueOf(currentChar);
            java.awt.datatransfer.StringSelection selection =
                new java.awt.datatransfer.StringSelection(charStr);
            java.awt.Toolkit.getDefaultToolkit().getSystemClipboard()
                .setContents(selection, null);

            // 更新状态
            updateStatus(String.format("已输出字符: '%s' (%d/%d)",
                currentChar == '\n' ? "\\n" :
                currentChar == '\t' ? "\\t" :
                currentChar == '\r' ? "\\r" : String.valueOf(currentChar),
                currentCharIndex + 1, currentText.length()));

            currentCharIndex++;
            updatePreviewWindow();

            // 如果是最后一个字符，完成输出
            if (currentCharIndex >= currentText.length()) {
                Timer timer = new Timer(1000, e -> stopCharByCharMode());
                timer.setRepeats(false);
                timer.start();
            }

        } catch (Exception e) {
            updateStatus("输出字符失败: " + e.getMessage());
            stopCharByCharMode();
        }
    }

    private void updatePreviewWindow() {
        if (currentCharIndex < currentText.length()) {
            char nextChar = currentText.charAt(currentCharIndex);
            String displayChar = nextChar == '\n' ? "↵" :
                                nextChar == '\t' ? "→" :
                                nextChar == '\r' ? "⏎" :
                                nextChar == ' ' ? "␣" : String.valueOf(nextChar);
            previewLabel.setText(displayChar);
            previewWindow.pack();
        }
    }

    private void stopCharByCharMode() {
        isCharByCharActive = false;
        if (!isSmartInputMode) {
            previewWindow.setVisible(false);
        }
        updateStatus("逐字符模式已结束");
    }

    // 智能输入助手功能
    private void startSmartInputMode() {
        ClipboardHistory.ClipboardItem selected = historyList.getSelectedValue();
        if (selected != null) {
            String content = selected.getContent();
            setupSmartInput(content);
        } else {
            updateStatus("请先选择要练习的文本");
        }
    }

    private void setupSmartInput(String content) {
        // 将文本分割成词语
        currentWords = content.split("\\s+|(?<=\\W)|(?=\\W)");
        currentWordIndex = 0;
        currentTypedWord.setLength(0);

        updateStatus("智能输入助手已启动，开始监控剪切板输入");
        showNextWord();
        previewWindow.setVisible(true);

        // 启动剪切板监控定时器
        if (inputTimer != null) {
            inputTimer.stop();
        }
        inputTimer = new Timer(200, e -> checkTypingProgress());
        inputTimer.start();
    }

    private void showNextWord() {
        if (currentWordIndex < currentWords.length) {
            String nextWord = currentWords[currentWordIndex];

            // 判断是否为中文
            boolean isChinese = nextWord.matches(".*[\\u4e00-\\u9fa5].*");

            // 设置显示样式
            if (isChinese) {
                previewLabel.setBackground(new Color(0, 150, 0, 200)); // 绿色背景
                previewLabel.setText("中文: " + nextWord);
            } else {
                previewLabel.setBackground(new Color(0, 100, 200, 200)); // 蓝色背景
                previewLabel.setText(nextWord);
            }

            previewWindow.pack();
            updateStatus(String.format("下一个词: %s (%d/%d)", nextWord, currentWordIndex + 1, currentWords.length));
        } else {
            // 完成所有输入
            previewLabel.setBackground(new Color(0, 200, 0, 200)); // 亮绿色
            previewLabel.setText("✓ 完成!");
            previewWindow.pack();
            updateStatus("恭喜！所有文本输入完成");

            // 3秒后自动隐藏
            Timer hideTimer = new Timer(3000, e -> stopSmartInputMode());
            hideTimer.setRepeats(false);
            hideTimer.start();
        }
    }

    private void checkTypingProgress() {
        try {
            // 获取当前剪切板内容
            String clipboardContent = getCurrentClipboardText();
            if (clipboardContent != null && !clipboardContent.isEmpty()) {
                analyzeTyping(clipboardContent);
            }
        } catch (Exception e) {
            // 静默处理异常
        }
    }

    private String getCurrentClipboardText() {
        try {
            java.awt.datatransfer.Clipboard clipboard = java.awt.Toolkit.getDefaultToolkit().getSystemClipboard();
            if (clipboard.isDataFlavorAvailable(java.awt.datatransfer.DataFlavor.stringFlavor)) {
                return (String) clipboard.getData(java.awt.datatransfer.DataFlavor.stringFlavor);
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return null;
    }

    private void analyzeTyping(String typedText) {
        if (currentWordIndex >= currentWords.length) {
            return;
        }

        String expectedWord = currentWords[currentWordIndex];

        // 检查是否输入了完整的词
        if (typedText.equals(expectedWord)) {
            // 输入正确，显示绿色提示
            showCorrectFeedback();
            currentWordIndex++;
            showNextWord();
        } else if (typedText.length() > expectedWord.length() ||
                  (!expectedWord.startsWith(typedText) && !typedText.isEmpty())) {
            // 输入错误，显示红色提示
            showErrorFeedback();
        }
    }

    private void showCorrectFeedback() {
        // 临时显示绿色圆点
        previewLabel.setBackground(new Color(0, 255, 0, 220));
        previewLabel.setText("✓");
        previewWindow.pack();

        Timer resetTimer = new Timer(300, e -> showNextWord());
        resetTimer.setRepeats(false);
        resetTimer.start();
    }

    private void showErrorFeedback() {
        // 临时显示红色圆点
        previewLabel.setBackground(new Color(255, 0, 0, 220));
        previewLabel.setText("✗");
        previewWindow.pack();

        Timer resetTimer = new Timer(500, e -> showNextWord());
        resetTimer.setRepeats(false);
        resetTimer.start();
    }

    private void stopSmartInputMode() {
        isSmartInputMode = false;
        if (inputTimer != null) {
            inputTimer.stop();
            inputTimer = null;
        }
        if (!isCharByCharActive) {
            previewWindow.setVisible(false);
        }
        updateStatus("智能输入助手已停止");
    }

    private void showCodeInputDialog() {
        JDialog dialog = new JDialog(this, "录入要练习的代码", true);
        dialog.setSize(600, 500);
        dialog.setLocationRelativeTo(this);

        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(new EmptyBorder(15, 15, 15, 15));

        // 标题和说明
        JLabel titleLabel = new JLabel("请输入要练习的代码：");
        titleLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 14));
        mainPanel.add(titleLabel, BorderLayout.NORTH);

        // 代码输入区域
        JTextArea codeArea = new JTextArea();
        codeArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 14));
        codeArea.setTabSize(4);
        codeArea.setLineWrap(false);
        codeArea.setWrapStyleWord(false);

        // 添加一些示例代码
        codeArea.setText("// 在这里输入你要练习的代码\n" +
                        "function calculateSum(numbers) {\n" +
                        "    let total = 0;\n" +
                        "    for (let i = 0; i < numbers.length; i++) {\n" +
                        "        total += numbers[i];\n" +
                        "    }\n" +
                        "    return total;\n" +
                        "}");

        JScrollPane scrollPane = new JScrollPane(codeArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        // 选项面板
        JPanel optionsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        JCheckBox autoStartCheckBox = new JCheckBox("录入后自动启动智能助手", true);
        optionsPanel.add(autoStartCheckBox);

        JCheckBox addToHistoryCheckBox = new JCheckBox("添加到历史记录", true);
        optionsPanel.add(addToHistoryCheckBox);

        mainPanel.add(optionsPanel, BorderLayout.SOUTH);

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));

        JButton cancelButton = new JButton("取消");
        cancelButton.addActionListener(e -> dialog.dispose());

        JButton confirmButton = new JButton("确定");
        confirmButton.addActionListener(e -> {
            String code = codeArea.getText().trim();
            if (!code.isEmpty()) {
                // 添加到历史记录
                if (addToHistoryCheckBox.isSelected()) {
                    history.addItem(code);
                    updateStatus("代码已添加到历史记录");
                }

                // 自动启动智能助手
                if (autoStartCheckBox.isSelected()) {
                    // 选中刚添加的项目
                    SwingUtilities.invokeLater(() -> {
                        if (listModel.getSize() > 0) {
                            historyList.setSelectedIndex(0); // 选择最新的项目
                            isSmartInputMode = true;
                            setupSmartInput(code);
                        }
                    });
                }

                dialog.dispose();
            } else {
                JOptionPane.showMessageDialog(dialog, "请输入代码内容", "提示", JOptionPane.WARNING_MESSAGE);
            }
        });

        buttonPanel.add(cancelButton);
        buttonPanel.add(confirmButton);

        // 组装对话框
        dialog.setLayout(new BorderLayout());
        dialog.add(mainPanel, BorderLayout.CENTER);
        dialog.add(buttonPanel, BorderLayout.SOUTH);

        // 设置默认按钮和快捷键
        dialog.getRootPane().setDefaultButton(confirmButton);

        // 添加Ctrl+Enter快捷键
        codeArea.getInputMap().put(KeyStroke.getKeyStroke(KeyEvent.VK_ENTER, KeyEvent.CTRL_DOWN_MASK), "confirm");
        codeArea.getActionMap().put("confirm", new AbstractAction() {
            @Override
            public void actionPerformed(ActionEvent e) {
                confirmButton.doClick();
            }
        });

        dialog.setVisible(true);
    }

    private Image createDefaultIcon() {
        // 创建一个简单的默认图标
        BufferedImage icon = new BufferedImage(16, 16, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = icon.createGraphics();
        g2d.setColor(Color.BLUE);
        g2d.fillRect(2, 2, 12, 12);
        g2d.setColor(Color.WHITE);
        g2d.fillRect(4, 4, 8, 8);
        g2d.dispose();
        return icon;
    }

    /**
     * 自定义列表渲染器
     */
    private class ClipboardItemRenderer extends DefaultListCellRenderer {
        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value,
                int index, boolean isSelected, boolean cellHasFocus) {
            super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
            
            if (value instanceof ClipboardHistory.ClipboardItem) {
                ClipboardHistory.ClipboardItem item = (ClipboardHistory.ClipboardItem) value;
                String text = String.format("[%s] %s", 
                        dateFormat.format(item.getTimestamp()),
                        item.getPreview());
                setText(text);
            }
            
            return this;
        }
    }
}
