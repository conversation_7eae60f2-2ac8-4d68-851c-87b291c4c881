# 剪切板工具 v1.0

一个简洁实用的剪切板历史记录管理工具，支持后台运行、历史记录管理和数据导出导入。

## 功能特性

- **实时监控** - 自动监控系统剪切板变化
- **历史记录** - 按复制顺序保存剪切板内容（最先复制的先输出）
- **后台运行** - 支持系统托盘，可隐藏到后台工作
- **数据管理** - 支持JSON格式的历史记录导出和导入
- **简洁界面** - 清爽易用的图形界面
- **快速操作** - 双击列表项快速复制，右键菜单操作

## 使用说明

### 启动程序
```bash
# 编译
javac -d out -cp src src/main/java/com/clipboard/*.java

# 运行
java -cp out com.clipboard.ClipboardManager
```

### 主要操作

1. **查看历史** - 程序会自动记录剪切板变化，在主界面列表中显示
2. **复制内容** - 双击列表项或选中后点击"复制选中"按钮
3. **删除记录** - 选中记录后点击"删除选中"按钮
4. **清空历史** - 点击"清空历史"按钮清除所有记录
5. **导出数据** - 点击"导出"按钮将历史记录保存为JSON文件
6. **导入数据** - 点击"导入"按钮从JSON文件恢复历史记录
7. **隐藏程序** - 点击"隐藏到托盘"或关闭窗口，程序会在系统托盘继续运行

### 系统托盘操作

- **双击托盘图标** - 显示主窗口
- **右键托盘图标** - 显示快捷菜单
  - 显示主窗口
  - 清空历史记录
  - 导出历史记录
  - 关于
  - 退出

## 技术特点

- **纯Java实现** - 无需额外依赖，跨平台兼容
- **轻量级设计** - 内存占用小，运行效率高
- **线程安全** - 多线程环境下稳定运行
- **异常处理** - 完善的错误处理机制

## 系统要求

- Java 8 或更高版本
- 支持系统托盘的操作系统（Windows、Linux、macOS）

## 项目结构

```
src/main/java/com/clipboard/
├── ClipboardManager.java      # 主程序入口
├── ClipboardHistory.java      # 历史记录管理
├── ClipboardMonitor.java      # 剪切板监控
├── ClipboardGUI.java          # 主界面
├── SystemTrayManager.java     # 系统托盘管理
└── DataManager.java           # 数据导出导入
```

## 开发说明

### 编译和运行

1. 确保安装了Java开发环境（JDK 8+）
2. 在项目根目录执行编译命令
3. 运行主程序

### 自定义配置

- 历史记录最大数量：在 `ClipboardHistory` 构造函数中修改
- 监控间隔：在 `ClipboardMonitor` 中修改定时器间隔
- 界面样式：在 `ClipboardGUI` 中自定义

## 注意事项

- 程序需要访问系统剪切板权限
- 某些安全软件可能会拦截剪切板访问
- 大量文本内容可能影响程序性能
- 建议定期清理历史记录以保持最佳性能

## 更新日志

### v1.0 (2025-07-31)
- 初始版本发布
- 实现基本的剪切板监控功能
- 支持历史记录管理
- 添加系统托盘支持
- 实现数据导出导入功能

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**开发者**: AI Assistant  
**版本**: 1.0  
**更新时间**: 2025-07-31
